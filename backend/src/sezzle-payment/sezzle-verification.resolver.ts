import { Resolver, Mutation, Args } from '@nestjs/graphql';
import { Allow, Permission, RequestContext, Logger, PaymentService, Ctx, OrderService, Order } from '@vendure/core';

@Resolver()
export class SezzleVerificationResolver {
  constructor(
    private paymentService: PaymentService,
    private orderService: OrderService,
  ) {}

  @Mutation()
  @Allow(Permission.Owner)
  async verifySezzlePayment(
    @Ctx() ctx: RequestContext,
    @Args() args: { orderCode: string }
  ): Promise<{ success: boolean; message: string }> {
    try {
      Logger.info(`[Sezzle] Verifying payment for order: ${args.orderCode}`, 'SezzleVerificationResolver');

      // Use OrderService to find order with proper hydration
      const order = await this.orderService.findOneByCode(ctx, args.orderCode, ['payments']);

      if (!order) {
        Logger.error(`[Sezzle] Order not found: ${args.orderCode}`, 'SezzleVerificationResolver');
        return { success: false, message: 'Order not found' };
      }

      Logger.info(`[Sezzle] Order found: ${args.orderCode}, ID: ${order.id}`, 'SezzleVerificationResolver');

      // Find the Sezzle payment
      const sezzlePayment = order.payments?.find((p: any) => p.method === 'sezzle' && p.state === 'Authorized');
      
      if (!sezzlePayment) {
        Logger.error(`[Sezzle] No authorized Sezzle payment found for order: ${args.orderCode}`, 'SezzleVerificationResolver');
        return { success: false, message: 'No authorized Sezzle payment found' };
      }

      Logger.info(`[Sezzle] Found Sezzle payment ${sezzlePayment.id} for order ${args.orderCode}`, 'SezzleVerificationResolver');

      // Attempt to settle the payment (this will verify with Sezzle and update status)
      const settlementResult = await this.paymentService.settlePayment(ctx, sezzlePayment.id);

      Logger.info(`[Sezzle] Settlement result type: ${JSON.stringify(settlementResult)}`, 'SezzleVerificationResolver');

      // Check if settlement was successful
      // SettlePaymentErrorResult has 'success: false' and 'errorMessage'
      // Payment object (success) has properties like 'id', 'state', etc.
      if (settlementResult && typeof settlementResult === 'object' && 'success' in settlementResult) {
        if (settlementResult.success === false) {
          // This is a SettlePaymentErrorResult
          const errorMessage = (settlementResult as any).errorMessage || 'Payment settlement failed';
          Logger.error(`[Sezzle] Payment settlement failed for order ${args.orderCode}: ${errorMessage}`, 'SezzleVerificationResolver');
          return { success: false, message: errorMessage };
        } else {
          // This is a successful SettlePaymentResult
          Logger.info(`[Sezzle] Payment settled successfully for order ${args.orderCode}`, 'SezzleVerificationResolver');

          // Reload the order to get the updated payment status
          const updatedOrder = await this.orderService.findOneByCode(ctx, args.orderCode, ['payments']);
          if (!updatedOrder) {
            Logger.error(`[Sezzle] Could not reload order ${args.orderCode} after settlement`, 'SezzleVerificationResolver');
            return { success: false, message: 'Could not verify payment settlement' };
          }

          // Check if payment is actually settled
          const settledPayment = updatedOrder.payments?.find(p => p.method === 'sezzle' && p.state === 'Settled');
          if (!settledPayment) {
            Logger.warn(`[Sezzle] Payment settlement succeeded but payment not yet in Settled state for order ${args.orderCode}`, 'SezzleVerificationResolver');
            return { success: false, message: 'Payment settlement in progress, please wait' };
          }

          // Try to transition order to PaymentSettled state
          try {
            const transitionResult = await this.orderService.transitionToState(ctx, updatedOrder.id, 'PaymentSettled');
            if (transitionResult instanceof Order) {
              Logger.info(`[Sezzle] Order ${args.orderCode} transitioned to PaymentSettled state`, 'SezzleVerificationResolver');
              return { success: true, message: 'Payment verified and order completed successfully', paymentSettled: true };
            } else {
              Logger.warn(`[Sezzle] Failed to transition order ${args.orderCode} to PaymentSettled: ${(transitionResult as any).message}`, 'SezzleVerificationResolver');
              return { success: true, message: 'Payment settled but order state transition pending', paymentSettled: true };
            }
          } catch (transitionError: any) {
            Logger.warn(`[Sezzle] Order state transition error for ${args.orderCode}: ${transitionError.message}`, 'SezzleVerificationResolver');
            return { success: true, message: 'Payment settled but order state transition pending', paymentSettled: true };
          }
        }
      } else if (settlementResult && typeof settlementResult === 'object' && 'id' in settlementResult) {
        // This is a Payment object (successful settlement)
        Logger.info(`[Sezzle] Payment settled successfully for order ${args.orderCode}`, 'SezzleVerificationResolver');

        // Try to transition order to PaymentSettled state
        try {
          const transitionResult = await this.orderService.transitionToState(ctx, order.id, 'PaymentSettled');
          if (transitionResult instanceof Order) {
            Logger.info(`[Sezzle] Order ${args.orderCode} transitioned to PaymentSettled state`, 'SezzleVerificationResolver');
          } else {
            Logger.warn(`[Sezzle] Failed to transition order ${args.orderCode} to PaymentSettled: ${(transitionResult as any).message}`, 'SezzleVerificationResolver');
          }
        } catch (transitionError: any) {
          Logger.warn(`[Sezzle] Order state transition error for ${args.orderCode}: ${transitionError.message}`, 'SezzleVerificationResolver');
        }

        return { success: true, message: 'Payment verified and settled successfully' };
      } else {
        // Unknown result type
        Logger.error(`[Sezzle] Unknown settlement result type for order ${args.orderCode}`, 'SezzleVerificationResolver');
        return { success: false, message: 'Unknown settlement result' };
      }
    } catch (error: any) {
      Logger.error(`[Sezzle] Payment verification error for order ${args.orderCode}: ${error.message}`, 'SezzleVerificationResolver');
      return { success: false, message: `Verification failed: ${error.message}` };
    }
  }
}
